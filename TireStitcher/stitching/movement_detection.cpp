#include "movement_detection.h"
#include "strip_extraction.h"
#include "debug_utils.h"

#include <opencv2/opencv.hpp>
#include <opencv2/imgproc.hpp>
#include <opencv2/video/tracking.hpp>

#include <numeric>
#include <algorithm>
#include <iomanip>
#include <sstream>
#include <cmath>

// Constants for template matching
const double PI = 3.14159265358979323846;

double calculateCorrelation(const cv::Mat& img1, const cv::Mat& img2, const StitchConfig& config) {
    cv::Mat gray1, gray2;

    // Convert to grayscale if needed
    if (img1.channels() == 3) {
        cv::cvtColor(img1, gray1, cv::COLOR_BGR2GRAY);
    } else {
        gray1 = img1.clone();
    }

    if (img2.channels() == 3) {
        cv::cvtColor(img2, gray2, cv::COLOR_BGR2GRAY);
    } else {
        gray2 = img2.clone();
    }

    // Use multiple templates of different sizes for more robust correlation
    int height = gray1.rows;
    int width = gray1.cols;

    // Use three different templates at different positions
    std::vector<double> sizes = {0.1, 0.2, 0.3};
    std::vector<double> positions = {0.3, 0.5, 0.7};
    std::vector<double> correlations;

    for (double size : sizes) {
        for (double pos : positions) {
            int templateWidth = static_cast<int>(width * size);
            int centerX = static_cast<int>(width * pos);
            int templateStart = std::max(0, centerX - templateWidth / 2);
            int templateEnd = std::min(width, templateStart + templateWidth);

            // Skip if template size is invalid
            if (templateEnd - templateStart < 10) continue;

            // Extract template
            cv::Mat templ = gray1(cv::Rect(templateStart, 0, templateEnd - templateStart, height));

            // Run template matching
            cv::Mat result;
            cv::matchTemplate(gray2, templ, result, cv::TM_CCOEFF_NORMED);

            // Get maximum correlation value
            double maxVal;
            cv::Point maxLoc;
            cv::minMaxLoc(result, nullptr, &maxVal, nullptr, &maxLoc);

            // Sub-pixel refinement for increased precision
            if (maxLoc.x > 1 && maxLoc.x < result.cols - 2) {
                // Get 5 points around maximum for better interpolation
                double p0 = maxLoc.x >= 2 ? result.at<float>(maxLoc.y, maxLoc.x - 2) : 0;
                double p1 = result.at<float>(maxLoc.y, maxLoc.x - 1);
                double p2 = result.at<float>(maxLoc.y, maxLoc.x);     // Center (max value)
                double p3 = result.at<float>(maxLoc.y, maxLoc.x + 1);
                double p4 = maxLoc.x < result.cols - 2 ? result.at<float>(maxLoc.y, maxLoc.x + 2) : 0;

                // Enhanced interpolation - using 5 points when available, fallback to 3
                if (maxLoc.x >= 2 && maxLoc.x < result.cols - 2) {
                    // Use 5-point quadratic fit (more accurate)
                    double a = (p0 - 4*p1 + 6*p2 - 4*p3 + p4) / 24.0;
                    double b = (-p0 + 8*p1 - 8*p3 + p4) / 12.0;
                    double c = (-p0 + 16*p1 - 30*p2 + 16*p3 - p4) / 24.0;

                    // If quadratic coefficient is valid (parabola has maximum)
                    if (std::abs(a) > 1e-6) {
                        double deltaX = -b / (2.0 * a);

                        // Only use if the refined position is reasonable
                        if (std::abs(deltaX) < 2.0) {
                            // Compute refined value
                            double refinedVal = p2 + b * deltaX + a * deltaX * deltaX;

                            // Use refined value if better than original
                            if (refinedVal > maxVal) {
                                maxVal = refinedVal;
                            }
                        }
                    }
                } else {
                    // Fallback to 3-point quadratic interpolation
                    double denominator = 2.0 * (p1 + p3 - 2.0 * p2);
                    if (std::abs(denominator) > 1e-6) {
                        double deltaX = (p1 - p3) / denominator;

                        // Only use if the refined position is reasonable
                        if (std::abs(deltaX) < 1.0) {
                            double refinedVal = p2 - 0.25 * deltaX * deltaX * denominator;

                            // Use refined value if better than original
                            if (refinedVal > maxVal) {
                                maxVal = refinedVal;
                            }
                        }
                    }
                }
            }

            // Clamp correlation to valid range [0, 1]
            maxVal = std::max(0.0, std::min(1.0, maxVal));
            correlations.push_back(maxVal);

            if (config.verbose) {
                std::stringstream ss;
                ss << std::fixed << std::setprecision(6);
                ss << "Template correlation at size=" << size << ", pos=" << pos
                   << ": " << maxVal;
                safePrint(ss.str(), false, config);
            }
        }
    }

    // If no valid correlations, return zero
    if (correlations.empty()) {
        return 0.0;
    }

    // Return the median correlation for robustness
    std::sort(correlations.begin(), correlations.end());
    size_t mid = correlations.size() / 2;
    if (correlations.size() % 2 == 0) {
        return (correlations[mid-1] + correlations[mid]) / 2.0;
    } else {
        return correlations[mid];
    }
}

void runTemplateMatching(const cv::Mat& img1, const cv::Mat& img2,
        std::vector<double>& allMovements,
        std::vector<double>& allCorrelations,
        std::vector<double>& allConfidences,
        const StitchConfig& config) {
    cv::Mat gray1, gray2;

    // Umwandlung in Graustufen wie gehabt
    if (img1.channels() == 3) {
        cv::cvtColor(img1, gray1, cv::COLOR_BGR2GRAY);
    } else {
        gray1 = img1.clone();
    }

    if (img2.channels() == 3) {
        cv::cvtColor(img2, gray2, cv::COLOR_BGR2GRAY);
    } else {
        gray2 = img2.clone();
    }

    int height = gray1.rows;
    int width = gray1.cols;

    // Differenziertere Template-Größen und Positionen für eine bessere Abdeckung
    std::vector<double> templateSizes = {0.03, 0.05, 0.07, 0.11, 0.15, 0.2};
    std::vector<double> templatePositions = {0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8, 0.9};

    // Vektoren leeren
    allMovements.clear();
    allCorrelations.clear();
    allConfidences.clear();

    // Parallele Template-Matching-Ausführung
    #pragma omp parallel for collapse(2)
    for (int s = 0; s < templateSizes.size(); s++) {
        for (int p = 0; p < templatePositions.size(); p++) {
            double size = templateSizes[s];
            double pos = templatePositions[p];

            int templateWidth = static_cast<int>(width * size);
            if (templateWidth < 10) continue; // Zu kleine Templates überspringen

            // Präzise Position berechnen
            double exactCenterPos = width * pos;
            int centerPos = static_cast<int>(exactCenterPos);

            int templateStart = std::max(0, centerPos - templateWidth / 2);
            int templateEnd = std::min(width, templateStart + templateWidth);

            // Ungültige Template-Dimensionen überspringen
            if (templateEnd - templateStart < 10) continue;

            // Template extrahieren
            cv::Mat templ = gray1(cv::Rect(templateStart, 0, templateEnd - templateStart, height));

            // VERBESSERT: Qualität des Templates für Konfidenzgewichtung bewerten
            cv::Mat templateEdges, templateLaplacian;
            cv::Sobel(templ, templateEdges, CV_16S, 1, 1);
            cv::convertScaleAbs(templateEdges, templateEdges);
            cv::Laplacian(templ, templateLaplacian, CV_16S);
            cv::convertScaleAbs(templateLaplacian, templateLaplacian);

            // 1. Kanten-basierte Bewertung
            double edgeMean = cv::mean(templateEdges)[0] / 255.0;

            // 2. Textur-Komplexität über Laplacian
            double textureMean = cv::mean(templateLaplacian)[0] / 255.0;

            // 3. Varianz-basierte Bewertung für Kontrast
            cv::Scalar mean, stddev;
            cv::meanStdDev(templ, mean, stddev);
            double varianceNorm = std::min(1.0, stddev[0] / 50.0);

            // 4. Kombinierte Qualitätsbewertung mit stärkerer Gewichtung von Kanten
            double templateQuality = std::min(1.0, (edgeMean * 2.0 + textureMean + varianceNorm) / 4.0);

            // NEU: Bei sehr niedrigem Detailreichtum Template überspringen
            if (templateQuality < 0.05) continue;

            // Suchbereich mit zusätzlichem Rand für Subpixel-Verfeinerung
            int searchMargin = templateWidth * 1.5; // Größerer Suchbereich für mehr Robustheit
            int searchStart = std::max(0, templateStart - searchMargin);
            int searchEnd = std::min(width, templateEnd + searchMargin);

            // Zu kleinen Suchbereich überspringen
            if (searchEnd - searchStart <= templateWidth) continue;

            cv::Mat searchArea = gray2(cv::Rect(searchStart, 0, searchEnd - searchStart, height));

            try {
                // Template-Matching
                cv::Mat result;
                cv::matchTemplate(searchArea, templ, result, cv::TM_CCOEFF_NORMED);

                // Initiale Bestimmung des Maximums
                double maxVal;
                cv::Point maxLoc;
                cv::minMaxLoc(result, nullptr, &maxVal, nullptr, &maxLoc);

                // VERBESSERT: Erweiterte Subpixel-Verfeinerung
                cv::Point2d refinedMaxLoc = maxLoc;

                // Nur Verfeinerung durchführen, wenn genügend umliegende Punkte vorhanden sind
                if (maxLoc.x > 1 && maxLoc.x < result.cols - 2) {
                    // 5-Punkt-Interpolation für höhere Genauigkeit
                    double p0 = maxLoc.x >= 2 ? result.at<float>(maxLoc.y, maxLoc.x - 2) : 0;
                    double p1 = result.at<float>(maxLoc.y, maxLoc.x - 1);
                    double p2 = result.at<float>(maxLoc.y, maxLoc.x); // Zentrum (Maximum)
                    double p3 = result.at<float>(maxLoc.y, maxLoc.x + 1);
                    double p4 = maxLoc.x < result.cols - 2 ? result.at<float>(maxLoc.y, maxLoc.x + 2) : 0;

                    // NEU: Prüfe ob die Korrelationswerte eine klare Spitze bilden
                    double peakStrength = (p2 - (p1 + p3) / 2.0) / p2;

                    // Bei schwacher Spitze: Weniger Vertrauen in dieses Template
                    if (peakStrength < 0.05) {
                        templateQuality *= 0.5;
                    }

                    // Polynomial-Fit für genauere Subpixel-Position
                    if (maxLoc.x >= 2 && maxLoc.x < result.cols - 2) {
                        double a = (p0 - 4*p1 + 6*p2 - 4*p3 + p4) / 24.0;
                        double b = (-p0 + 8*p1 - 8*p3 + p4) / 12.0;
                        double c = (-p0 + 16*p1 - 30*p2 + 16*p3 - p4) / 24.0;

                        if (std::abs(a) > 1e-6) { // Sicherstellen, dass Parabel ein Maximum hat
                            double deltaX = -b / (2.0 * a);

                            // Auf vernünftige Werte beschränken
                            if (std::abs(deltaX) < 2.0) {
                                refinedMaxLoc.x = maxLoc.x + deltaX;

                                // Verfeinerten Maximalwert berechnen
                                double refinedMaxVal = p2 + b * deltaX + a * deltaX * deltaX;
                                if (refinedMaxVal > maxVal) {
                                    maxVal = refinedMaxVal;
                                }
                            }
                        }
                    }
                }

                // NEU: Auch y-Koordinate verfeinern
                if (maxLoc.y > 0 && maxLoc.y < result.rows - 1) {
                    double top = result.at<float>(maxLoc.y - 1, maxLoc.x);
                    double center = result.at<float>(maxLoc.y, maxLoc.x);
                    double bottom = result.at<float>(maxLoc.y + 1, maxLoc.x);

                    double denominator = 2.0 * (top + bottom - 2.0 * center);
                    if (std::abs(denominator) > 1e-6) {
                        double deltaY = (top - bottom) / denominator;

                        if (std::abs(deltaY) < 1.0) {
                            refinedMaxLoc.y = maxLoc.y + deltaY;
                        }
                    }
                }

                // Bewegung mit Subpixel-Präzision berechnen
                double templateCenter = templateWidth / 2.0;
                double matchCenter = refinedMaxLoc.x + templateCenter;
                double searchCenter = (searchEnd - searchStart) / 2.0;

                double movement = matchCenter - searchCenter;

                // VERBESSERT: Differenziertere Konfidenzberechnung basierend auf Korrelation und Template-Qualität
                // Quadratische Gewichtung der Korrelation + Template-Qualität als Faktor
                double confidenceWeight = maxVal * maxVal * (0.2 + 0.8 * templateQuality);

                // NEU: Prüfe Plausibilität der Bewegung im Vergleich zu anderen Templates
                // Use PROVEN different thresholds for 4K vs 8K mode
                double correlationThreshold = (config.resolutionMode == "4K") ? 0.65 : 0.75; // PROVEN: More aggressive threshold for 4K
                if (maxVal > correlationThreshold) {
                    #pragma omp critical
                    {
                        allMovements.push_back(movement);
                        allCorrelations.push_back(maxVal);
                        allConfidences.push_back(confidenceWeight);
                    }
                }

                // NEU: Informative Debug-Ausgabe
                if (config.verbose && (maxVal > 0.8 || templateQuality > 0.7)) {
                    std::stringstream ss;
                    ss << std::fixed << std::setprecision(6);
                    ss << "High-quality template match at pos=" << pos << ", size=" << size
                       << ", quality=" << templateQuality
                       << ", correlation=" << maxVal
                       << ", confidence=" << confidenceWeight
                       << ", movement=" << movement;
                    safePrint(ss.str(), false, config);
                }
            }
            catch (const cv::Exception& e) {
                safePrint(std::string("Warning: Template matching failed: ") + e.what(), false, config);
            }
        }
    }
}

double extractMovementFromFlowDetailed(const cv::Mat& flow, bool horizontal, bool verbose) {
    // Debug-Ausgabe der Eingabematrix
    if (verbose) {
        debugFlowMatrix(flow, "Input flow matrix", true);
    }

    // Prüfen Sie, ob der Fluss leer ist
    if (flow.empty()) {
        safePrint("ERROR: Flow matrix is empty!", true);
        return 0.0;
    }

    // Extrahieren Sie den horizontalen oder vertikalen Fluss
    cv::Mat flowComponent;
    if (horizontal) {
        // Horizontale Komponente (x-Richtung)
        std::vector<cv::Mat> flowChannels;
        cv::split(flow, flowChannels);

        // Safety check
        if (flowChannels.size() < 2) {
            safePrint("ERROR: Flow matrix does not have enough channels", true);
            return 0.0;
        }

        flowComponent = flowChannels[0]; // x-Komponente

        if (verbose) {
            safePrint("Extracted horizontal flow component, shape: " +
                      std::to_string(flowComponent.rows) + "x" +
                      std::to_string(flowComponent.cols) +
                      ", type: " + std::to_string(flowComponent.type()), true);
        }
    } else {
        // Vertikale Komponente (y-Richtung)
        std::vector<cv::Mat> flowChannels;
        cv::split(flow, flowChannels);

        // Safety check
        if (flowChannels.size() < 2) {
            safePrint("ERROR: Flow matrix does not have enough channels", true);
            return 0.0;
        }

        flowComponent = flowChannels[1]; // y-Komponente
    }

    // ENHANCED: Use spatial weighting to prioritize center of image
    cv::Mat weights(flowComponent.size(), CV_32F);
    cv::Point2f center(flowComponent.cols / 2.0f, flowComponent.rows / 2.0f);
    float maxDistance = std::sqrt(center.x * center.x + center.y * center.y);

    for (int y = 0; y < flowComponent.rows; y++) {
        for (int x = 0; x < flowComponent.cols; x++) {
            float dx = x - center.x;
            float dy = y - center.y;
            float distance = std::sqrt(dx * dx + dy * dy);
            float weight = 1.0f - (distance / maxDistance) * 0.8f; // Center has weight 1.0, edges 0.2
            weights.at<float>(y, x) = weight;
        }
    }

    // Compute weighted statistics
    double weightedSum = 0.0;
    double sumWeights = 0.0;
    std::vector<std::pair<float, float>> valuePairs; // (value, weight) pairs for median calculation

    for (int y = 0; y < flowComponent.rows; y++) {
        for (int x = 0; x < flowComponent.cols; x++) {
            float value = flowComponent.at<float>(y, x);
            float weight = weights.at<float>(y, x);

            // Filter outliers and invalid values
            if (!std::isnan(value) && !std::isinf(value) && std::abs(value) < 100.0) {
                weightedSum += value * weight;
                sumWeights += weight;
                valuePairs.push_back(std::make_pair(value, weight));
            }
        }
    }

    // If no valid values found, return 0
    if (valuePairs.empty()) {
        safePrint("WARNING: No valid flow values found", true);
        return 0.0;
    }

    if (verbose) {
        safePrint("Collected " + std::to_string(valuePairs.size()) + " valid flow values with weights", true);

        // Output some sample values
        if (!valuePairs.empty()) {
            std::stringstream ss;
            ss << std::fixed << std::setprecision(12);
            ss << "Sample flow values (value, weight): [";
            for (int i = 0; i < std::min(5, static_cast<int>(valuePairs.size())); i++) {
                ss << "(" << valuePairs[i].first << ", " << valuePairs[i].second << ")";
                if (i < std::min(4, static_cast<int>(valuePairs.size())-1)) ss << ", ";
            }
            ss << "]";
            safePrint(ss.str(), true);
        }
    }

    // Compute weighted mean
    double weightedMean = weightedSum / sumWeights;

    // Compute weighted median
    // Sort by value
    std::sort(valuePairs.begin(), valuePairs.end(),
              [](const std::pair<float, float>& a, const std::pair<float, float>& b) {
                  return a.first < b.first;
              });

    // Find weighted median
    double targetWeight = sumWeights / 2.0;
    double cumulativeWeight = 0.0;
    double weightedMedian = 0.0;

    for (const auto& pair : valuePairs) {
        cumulativeWeight += pair.second;
        if (cumulativeWeight >= targetWeight) {
            weightedMedian = pair.first;
            break;
        }
    }

    // ENHANCED: Compute robust statistics for outlier detection
    double q1 = 0.0, q3 = 0.0;
    double cumulativeQ1 = sumWeights * 0.25;
    double cumulativeQ3 = sumWeights * 0.75;
    double cumulativeWeight2 = 0.0;

    for (const auto& pair : valuePairs) {
        cumulativeWeight2 += pair.second;
        if (cumulativeWeight2 >= cumulativeQ1 && q1 == 0.0) {
            q1 = pair.first;
        }
        if (cumulativeWeight2 >= cumulativeQ3) {
            q3 = pair.first;
            break;
        }
    }

    double iqr = q3 - q1;
    double lowerBound = q1 - 1.5 * iqr;
    double upperBound = q3 + 1.5 * iqr;

    // Debug output
    if (verbose) {
        std::stringstream ss;
        ss << std::fixed << std::setprecision(12);
        ss << "Flow statistics: weighted mean=" << weightedMean
           << ", weighted median=" << weightedMedian
           << ", Q1=" << q1 << ", Q3=" << q3
           << ", IQR=" << iqr
           << ", bounds=[" << lowerBound << ", " << upperBound << "]";
        safePrint(ss.str(), true);
    }

    // ENHANCED: Robust final estimate combining mean and median
    double finalMovement;

    // Check if mean is an outlier compared to median
    if (weightedMean < lowerBound || weightedMean > upperBound) {
        // Mean is an outlier, use median
        finalMovement = weightedMedian;
        if (verbose) safePrint("Using weighted median (mean is outlier)", true);
    } else {
        // Combine weighted mean and median
        finalMovement = 0.4 * weightedMean + 0.6 * weightedMedian;
        if (verbose) safePrint("Using combined estimate", true);
    }

    // Debug-Ausgabe des finalen Wertes
    if (verbose) {
        debugFloatingPoint("finalMovement", finalMovement);
        debugFloatingPoint("abs(finalMovement)", std::abs(finalMovement));
    }

    // Return absolute value
    return std::abs(finalMovement);
}

double measureMovementWithOpticalFlow(const cv::Mat& img1, const cv::Mat& img2,
                                     const StitchConfig& config,
                                     double* outCorrelation) {
    // Puffer für frühere Messungen
    static std::deque<double> recentFlowMovements(5, 0.0);
    static std::mutex flowMovementsMutex;

    // Bildgrößenanpassung für konsistente Verarbeitung und bessere Performance
    cv::Mat scaledImg1, scaledImg2;
    double scale = 0.25; // Reduktion auf 1/4 der Größe

    // Hochwertige Interpolation für präzise Skalierung
    cv::resize(img1, scaledImg1, cv::Size(), scale, scale, cv::INTER_LANCZOS4);
    cv::resize(img2, scaledImg2, cv::Size(), scale, scale, cv::INTER_LANCZOS4);

    // Korrelationsberechnung
    double correlation = calculateCorrelation(scaledImg1, scaledImg2, config);

    if (outCorrelation != nullptr) {
        *outCorrelation = correlation;
    }

    if (config.verbose) {
        std::stringstream corrStr;
        corrStr << std::fixed << std::setprecision(6);
        corrStr << "Image correlation: " << correlation;
        safePrint(corrStr.str(), config.verbose);
    }

    // NEU: Multi-Resolution-Ansatz - berechne die Bewegung auf mehreren Skalenebenen
    std::vector<double> multiScaleMovements;
    std::vector<double> multiScaleWeights;

    // Use PROVEN different correlation thresholds for multi-resolution approach
    double multiResThreshold = (config.resolutionMode == "4K") ? 0.70 : 0.8; // PROVEN: More aggressive threshold for 4K
    if (correlation >= multiResThreshold) {
        // Für hohe Korrelationen: Teste verschiedene Auflösungsstufen
        std::vector<double> scales = {0.15, 0.25, 0.4};

        for (double scaleLevel : scales) {
            cv::Mat levelImg1, levelImg2;
            cv::resize(img1, levelImg1, cv::Size(), scaleLevel, scaleLevel, cv::INTER_LANCZOS4);
            cv::resize(img2, levelImg2, cv::Size(), scaleLevel, scaleLevel, cv::INTER_LANCZOS4);

            std::vector<double> levelMovements;
            std::vector<double> levelCorrelations;
            std::vector<double> levelConfidences;

            runTemplateMatching(levelImg1, levelImg2, levelMovements, levelCorrelations, levelConfidences, config);

            if (!levelMovements.empty()) {
                // Gewichteter Durchschnitt für diese Auflösungsstufe
                double sumWeightedMoves = 0.0;
                double sumWeights = 0.0;

                for (size_t i = 0; i < levelMovements.size(); i++) {
                    sumWeightedMoves += levelMovements[i] * levelConfidences[i];
                    sumWeights += levelConfidences[i];
                }

                if (sumWeights > 0) {
                    double scaledAvgMovement = (sumWeightedMoves / sumWeights) / scaleLevel;

                    // Höhere Auflösungen (kleinere scaleLevel) haben höheres Gewicht
                    double scaleWeight = 1.0 / scaleLevel;

                    multiScaleMovements.push_back(scaledAvgMovement);
                    multiScaleWeights.push_back(scaleWeight);

                    if (config.verbose) {
                        safePrint("Scale " + std::to_string(scaleLevel) +
                                 " produced movement: " + std::to_string(scaledAvgMovement) +
                                 " from " + std::to_string(levelMovements.size()) + " measurements",
                                 config.verbose);
                    }
                }
            }
        }
    }

    // Verwende Multi-Scale-Ergebnisse, wenn verfügbar
    if (!multiScaleMovements.empty()) {
        // Gewichteter Durchschnitt über alle Skalen
        double sumMultiScaleMovement = 0.0;
        double sumMultiScaleWeights = 0.0;

        for (size_t i = 0; i < multiScaleMovements.size(); i++) {
            sumMultiScaleMovement += multiScaleMovements[i] * multiScaleWeights[i];
            sumMultiScaleWeights += multiScaleWeights[i];
        }

        double finalMultiScaleMovement = sumMultiScaleMovement / sumMultiScaleWeights;

        if (config.verbose) {
            safePrint("Multi-scale movement calculation: " + std::to_string(finalMultiScaleMovement),
                     config.verbose);
        }

        // Prüfe auf extreme Abweichungen von früheren Messungen
        bool movementIsConsistent = true;
        {
            std::lock_guard<std::mutex> lock(flowMovementsMutex);
            if (!recentFlowMovements.empty()) {
                double avgPrevMovement = 0.0;
                int count = 0;
                for (const auto& prevMove : recentFlowMovements) {
                    if (prevMove > 0) {
                        avgPrevMovement += prevMove;
                        count++;
                    }
                }

                if (count > 0) {
                    avgPrevMovement /= count;

                    // Prüfe, ob aktuelle Bewegung stark von vorherigen abweicht
                    if (std::abs(finalMultiScaleMovement - avgPrevMovement) > avgPrevMovement * 0.4) {
                        movementIsConsistent = false;

                        if (config.verbose) {
                            safePrint("WARNING: Movement value " + std::to_string(finalMultiScaleMovement) +
                                    " deviates significantly from previous average " +
                                    std::to_string(avgPrevMovement), true);
                        }

                        // Bei extremer Abweichung (>100%): Passe den Wert an
                        if (std::abs(finalMultiScaleMovement - avgPrevMovement) > avgPrevMovement) {
                            // Gewichte stärker in Richtung des durchschnittlichen früheren Werts
                            double adjustedMovement = (finalMultiScaleMovement * 0.3) + (avgPrevMovement * 0.7);

                            safePrint("Movement value adjusted from " + std::to_string(finalMultiScaleMovement) +
                                    " to " + std::to_string(adjustedMovement) + " for consistency", true);

                            finalMultiScaleMovement = adjustedMovement;
                        }
                    }
                }
            }

            // Aktualisiere den gleitenden Durchschnitt
            recentFlowMovements.pop_front();
            recentFlowMovements.push_back(finalMultiScaleMovement);
        }

        return std::abs(finalMultiScaleMovement);
    }

    // ENHANCED: Verbesserte Template-Matching-Strategie für hohe Korrelationen
    // Use PROVEN different high correlation thresholds for 4K vs 8K mode
    double highCorrelationThreshold = (config.resolutionMode == "4K") ? 0.80 : 0.92; // PROVEN: More aggressive threshold for 4K
    if (correlation >= highCorrelationThreshold) {
        std::vector<double> allMovements;
        std::vector<double> allCorrelations;
        std::vector<double> allConfidences;

        // Erweitertes Template-Matching mit Konfidenzwerten
        runTemplateMatching(scaledImg1, scaledImg2, allMovements, allCorrelations, allConfidences, config);

        if (!allMovements.empty()) {
            // NEU: Robuste Statistik mit verbesserter Ausreißererkennung

            // NEU: Entferne Ausreißer direkt basierend auf Verteilungsstatistiken
            // Indizes zum Sortieren der Bewegungen
            std::vector<size_t> indices(allMovements.size());
            std::iota(indices.begin(), indices.end(), 0);
            std::sort(indices.begin(), indices.end(),
                     [&allMovements](size_t a, size_t b) {
                         return allMovements[a] < allMovements[b];
                     });

            // Berechne Quartile für Ausreißererkennung
            double q1, q3, iqr;
            if (indices.size() >= 4) {
                size_t q1_idx = indices.size() / 4;
                size_t q3_idx = (3 * indices.size()) / 4;
                q1 = allMovements[indices[q1_idx]];
                q3 = allMovements[indices[q3_idx]];
                iqr = q3 - q1;
            } else {
                // Nicht genügend Daten für Quartile, verwende vollen Bereich
                q1 = allMovements[indices.front()];
                q3 = allMovements[indices.back()];
                iqr = q3 - q1;
            }

            // Definiere Ausreißergrenzen (strenger als Standard-IQR-Grenzen)
            double lowerBound = q1 - 1.2 * iqr;
            double upperBound = q3 + 1.2 * iqr;

            if (config.verbose) {
                std::stringstream ss;
                ss << std::fixed << std::setprecision(6);
                ss << "Outlier bounds: [" << lowerBound << ", " << upperBound << "]"
                   << ", Q1=" << q1 << ", Q3=" << q3 << ", IQR=" << iqr;
                safePrint(ss.str(), false, config);
            }

            // Filtere Ausreißer und berechne gewichteten Durchschnitt
            double sumWeightedMovements = 0.0;
            double sumWeights = 0.0;
            std::vector<double> filteredMovements;
            std::vector<double> filteredWeights;

            for (size_t i = 0; i < allMovements.size(); i++) {
                if (allMovements[i] >= lowerBound && allMovements[i] <= upperBound) {
                    filteredMovements.push_back(allMovements[i]);
                    filteredWeights.push_back(allConfidences[i]);

                    sumWeightedMovements += allMovements[i] * allConfidences[i];
                    sumWeights += allConfidences[i];
                }
            }

            if (filteredMovements.empty()) {
                safePrint("Warning: All movements were outliers! Using original values.", config.verbose);
                filteredMovements = allMovements;
                filteredWeights = allConfidences;

                // Gewichtete Summen neu berechnen
                sumWeightedMovements = 0.0;
                sumWeights = 0.0;
                for (size_t i = 0; i < filteredMovements.size(); i++) {
                    sumWeightedMovements += filteredMovements[i] * filteredWeights[i];
                    sumWeights += filteredWeights[i];
                }
            }

            // Berechne gewichteten Durchschnitt und Median
            double weightedAverage = sumWeightedMovements / sumWeights;

            // Berechne gewichteten Median
            std::vector<std::pair<double, double>> valueWeightPairs;
            for (size_t i = 0; i < filteredMovements.size(); i++) {
                valueWeightPairs.push_back(std::make_pair(filteredMovements[i], filteredWeights[i]));
            }

            std::sort(valueWeightPairs.begin(), valueWeightPairs.end(),
                     [](const std::pair<double, double>& a, const std::pair<double, double>& b) {
                         return a.first < b.first;
                     });

            double halfWeight = sumWeights / 2.0;
            double cumulativeWeight = 0.0;
            double weightedMedian = 0.0;

            for (const auto& pair : valueWeightPairs) {
                cumulativeWeight += pair.second;
                if (cumulativeWeight >= halfWeight) {
                    weightedMedian = pair.first;
                    break;
                }
            }

            // NEU: Maß für die Streuung der Werte
            double variability = 0.0;
            if (filteredMovements.size() >= 2) {
                double sum = 0.0;
                for (const auto& movement : filteredMovements) {
                    sum += std::pow(movement - weightedAverage, 2);
                }
                variability = std::sqrt(sum / filteredMovements.size()) / weightedAverage;
            }

            // Verwende gewichteten Median, wenn die Werte stark streuen oder der gewichtete Durchschnitt ein Ausreißer ist
            double finalMovement;
            double avgMedianDiff = std::abs(weightedAverage - weightedMedian);

            if (avgMedianDiff > 0.2 * std::abs(weightedMedian) || variability > 0.15) {
                // Signifikanter Unterschied oder hohe Variabilität - verwende Median für Stabilität
                finalMovement = weightedMedian;
                safePrint("Using median movement (weighted avg rejected due to high variability)", config.verbose);
            } else {
                // Ähnliche Ergebnisse - verwende gewichtete Kombination für Präzision
                finalMovement = weightedAverage * 0.6 + weightedMedian * 0.4;
                safePrint("Using weighted movement average for subpixel precision", config.verbose);
            }

            if (config.verbose) {
                std::stringstream ss;
                ss << std::fixed << std::setprecision(15);
                ss << "Movement calculation: final=" << finalMovement
                   << ", weighted avg=" << weightedAverage
                   << ", weighted median=" << weightedMedian
                   << ", variability=" << variability
                   << ", correlation=" << correlation
                   << ", measurements=" << filteredMovements.size()
                   << "/" << allMovements.size();
                safePrint(ss.str(), config.verbose);
            }

            // NEU: Prüfe Konsistenz mit früheren Messungen
            bool movementIsConsistent = true;
            {
                std::lock_guard<std::mutex> lock(flowMovementsMutex);
                if (!recentFlowMovements.empty()) {
                    double avgPrevMovement = 0.0;
                    int count = 0;
                    for (const auto& prevMove : recentFlowMovements) {
                        if (prevMove > 0) {
                            avgPrevMovement += prevMove;
                            count++;
                        }
                    }

                    if (count > 0) {
                        avgPrevMovement /= count;

                        // Prüfe auf extreme Abweichungen (>40%)
                        if (std::abs(finalMovement - avgPrevMovement) > avgPrevMovement * 0.4) {
                            movementIsConsistent = false;

                            if (config.verbose) {
                                safePrint("WARNING: Movement " + std::to_string(finalMovement) +
                                         " deviates significantly from previous average " +
                                         std::to_string(avgPrevMovement), true);
                            }

                            // Bei extremer Abweichung: Passe den Wert an
                            if (std::abs(finalMovement - avgPrevMovement) > avgPrevMovement) {
                                // Gewichtung zugunsten des historischen Durchschnitts
                                double adjustedMovement = (finalMovement * 0.25) + (avgPrevMovement * 0.75);

                                safePrint("Movement value adjusted from " + std::to_string(finalMovement) +
                                         " to " + std::to_string(adjustedMovement) + " for consistency", true);

                                finalMovement = adjustedMovement;
                            }
                        }
                    }
                }

                // Gleitenden Durchschnitt aktualisieren
                recentFlowMovements.pop_front();
                recentFlowMovements.push_back(finalMovement);
            }

            // Skaliere zurück auf Originalgröße
            double result = std::abs(finalMovement) / scale;

            if (config.verbose) {
                std::stringstream ss2;
                ss2 << std::fixed << std::setprecision(15);
                ss2 << "Scaled back movement: " << result;
                safePrint(ss2.str(), config.verbose);

                debugFloatingPoint("result (full precision)", result);
            }

            return result;
        }
    }

    // Fallback zu Optical Flow für niedrige Korrelationen oder wenn Template-Matching fehlschlägt
    safePrint("Using optical flow (correlation: " + std::to_string(correlation) + ")", config.verbose);

    // Verbesserte Multi-Scale Optical Flow Berechnung
    cv::Mat flow = calculateOpticalFlow(scaledImg1, scaledImg2);
    double flowMovement = extractMovementFromFlowDetailed(flow, true, config.verbose);

    if (config.verbose) {
        std::stringstream ss2;
        ss2 << std::fixed << std::setprecision(15);
        ss2 << "Raw optical flow movement: " << flowMovement;
        safePrint(ss2.str(), true);
    }

    // NEU: Laufenden Durchschnitt mit Thread-Sicherheit aktualisieren und Konsistenzprüfung
    {
        std::lock_guard<std::mutex> lock(flowMovementsMutex);

        // Verwerfe extreme Ausreißer sofort
        double avgPrevMovement = 0.0;
        int count = 0;
        for (const auto& prevMove : recentFlowMovements) {
            if (prevMove > 0) {
                avgPrevMovement += prevMove;
                count++;
            }
        }

        if (count > 0) {
            avgPrevMovement /= count;

            // Verwerfe extreme Ausreißer
            if (flowMovement > avgPrevMovement * 3.0) {
                // Bei extremen Spitzen: Nutze den historischen Durchschnitt
                safePrint("WARNING: Extreme outlier detected, flow value " + std::to_string(flowMovement) +
                         " exceeds 3x avg (" + std::to_string(avgPrevMovement) + ")", true);

                flowMovement = avgPrevMovement;
            }
        }

        // Ältesten Wert entfernen und neuen hinzufügen
        recentFlowMovements.pop_front();
        recentFlowMovements.push_back(flowMovement);

        // Gewichteter Durchschnitt mit Betonung neuerer Werte
        double weightedSum = 0.0;
        double totalWeight = 0.0;
        const double weights[5] = {0.1, 0.15, 0.2, 0.25, 0.3}; // Höhere Gewichte für neuere Werte

        for (int i = 0; i < recentFlowMovements.size(); i++) {
            weightedSum += recentFlowMovements[i] * weights[i];
            totalWeight += weights[i];
        }

        double averageFlowMovement = weightedSum / totalWeight;

        if (config.verbose) {
            std::stringstream ss3;
            ss3 << std::fixed << std::setprecision(15);
            ss3 << "Optical flow movement: " << flowMovement
                << ", weighted average: " << averageFlowMovement;
            safePrint(ss3.str(), config.verbose);

            debugFloatingPoint("averageFlowMovement", averageFlowMovement);
        }

        // NEU: Intelligentere Behandlung kleiner Bewegungen mit adaptiver Skalierung
        if (averageFlowMovement <= 5.0) {
            // Verwende eine nicht-lineare Skalierungsfunktion für kleine Bewegungen
            double defaultMovement = 75.0 + 10.0 * std::sqrt(averageFlowMovement);
            safePrint("Flow movement too small, using improved scaling: " +
                      std::to_string(defaultMovement), config.verbose);
            return defaultMovement;
        }

        // Bewegung zurück auf Originalgröße skalieren
        double scaledMovement = averageFlowMovement / scale;

        if (config.verbose) {
            std::stringstream ss4;
            ss4 << std::fixed << std::setprecision(15);
            ss4 << "Final scaled movement (raw): " << scaledMovement;
            safePrint(ss4.str(), true);

            debugFloatingPoint("scaledMovement (final)", scaledMovement);
        }

        return std::abs(scaledMovement);
    }
}

// Visualisiert die Qualität der Matches zwischen zwei Bildern
void visualizeMatchQuality(const cv::Mat& img1, const cv::Mat& img2,
                          const std::vector<double>& allMovements,
                          const std::vector<double>& allConfidences,
                          const std::string& outputPath,
                          const StitchConfig& config) {
    if (!config.verbose) return; // Nur ausführen, wenn verbose-Modus aktiv ist

    try {
        // Erstelle ein kombiniertes Bild
        cv::Mat visImg;

        // Beschränke die Bilddimensionen für bessere Visualisierung
        cv::Mat resizedImg1, resizedImg2;
        int maxHeight = 600;
        double scaleFactor = 1.0;

        if (img1.rows > maxHeight) {
            scaleFactor = static_cast<double>(maxHeight) / img1.rows;
            cv::resize(img1, resizedImg1, cv::Size(), scaleFactor, scaleFactor, cv::INTER_AREA);
            cv::resize(img2, resizedImg2, cv::Size(), scaleFactor, scaleFactor, cv::INTER_AREA);
        } else {
            resizedImg1 = img1.clone();
            resizedImg2 = img2.clone();
        }

        cv::hconcat(resizedImg1, resizedImg2, visImg);

        // Erstelle ein Overlay für Diagnostik-Informationen
        cv::Mat overlay = visImg.clone();

        // Zeichne Korrespondenzen zwischen den Bildern
        int numPoints = std::min(20, static_cast<int>(allMovements.size())); // Begrenze auf 20 Punkte für Übersichtlichkeit

        // Normalisiere Konfidenzwerte
        double maxConfidence = 0.0;
        for (double conf : allConfidences) {
            maxConfidence = std::max(maxConfidence, conf);
        }

        // Seeding für Zufallsgenerator
        std::srand(static_cast<unsigned int>(std::time(nullptr)));

        // Sammle Informationen für Textausgabe
        double avgMovement = 0.0;
        double avgConfidence = 0.0;

        // Visualisiere hochgewichtete Bewegungen
        for (int i = 0; i < numPoints && i < allMovements.size(); i++) {
            // Zufällige Y-Positionen für bessere Verteilung
            int y1 = std::rand() % (resizedImg1.rows - 40) + 20;

            // X-Positionen basierend auf der beobachteten Bewegung
            int x1 = resizedImg1.cols - 100 - (i % 5) * 30; // Verteilte Punkte auf der rechten Seite
            int x2 = 20 + (i % 5) * 30; // Verteilte Punkte auf der linken Seite

            // Berücksichtige die berechnete Bewegung, skaliert mit dem Faktor
            double scaledMovement = allMovements[i] * scaleFactor;
            x2 = std::max(0, std::min(resizedImg2.cols-1, static_cast<int>(x2 - scaledMovement)));

            // Berechne Punkte
            cv::Point p1(x1, y1);
            cv::Point p2(x2 + resizedImg1.cols, y1); // Offset für das zweite Bild

            // Farbe basierend auf Konfidenz (grün = hoch, rot = niedrig)
            double normConfidence = allConfidences[i] / maxConfidence;
            cv::Scalar color(0, 255 * normConfidence, 255 * (1.0 - normConfidence));

            // Zeichne Linie und Punkte
            cv::line(overlay, p1, p2, color, 2);
            cv::circle(overlay, p1, 5, color, -1);
            cv::circle(overlay, p2, 5, color, -1);

            // Label mit Bewegungswert und Konfidenz
            std::stringstream ss;
            ss << std::fixed << std::setprecision(1);
            ss << allMovements[i] << "px";
            cv::putText(overlay, ss.str(), cv::Point(p1.x - 40, p1.y - 10),
                       cv::FONT_HERSHEY_SIMPLEX, 0.5, color, 1);

            // Sammle Daten für Durchschnittswerte
            avgMovement += allMovements[i];
            avgConfidence += allConfidences[i];
        }

        // Berechne Durchschnittswerte
        if (numPoints > 0) {
            avgMovement /= numPoints;
            avgConfidence /= numPoints;
        }

        // Füge Informationstext hinzu
        std::vector<std::string> infoTexts;

        {
            std::stringstream ss;
            ss << std::fixed << std::setprecision(2);
            ss << "Green = High confidence matches, Red = Low confidence matches";
            infoTexts.push_back(ss.str());
        }

        {
            std::stringstream ss;
            ss << std::fixed << std::setprecision(2);
            ss << "Average movement: " << avgMovement << "px";
            infoTexts.push_back(ss.str());
        }

        {
            std::stringstream ss;
            ss << std::fixed << std::setprecision(4);
            ss << "Average confidence: " << avgConfidence;
            infoTexts.push_back(ss.str());
        }

        // Erstelle einen Panel-Hintergrund für Text
        int panelHeight = infoTexts.size() * 30 + 20;
        cv::rectangle(overlay, cv::Rect(10, 10, 500, panelHeight),
                     cv::Scalar(0, 0, 0), -1);
        cv::rectangle(overlay, cv::Rect(10, 10, 500, panelHeight),
                     cv::Scalar(255, 255, 255), 1);

        // Füge Texte hinzu
        for (int i = 0; i < infoTexts.size(); i++) {
            cv::putText(overlay, infoTexts[i], cv::Point(20, 35 + i * 30),
                       cv::FONT_HERSHEY_SIMPLEX, 0.7, cv::Scalar(255, 255, 255), 1);
        }

        // Blend overlay mit Originalbild
        double alpha = 0.7; // Transparenz
        cv::addWeighted(overlay, alpha, visImg, 1.0 - alpha, 0, visImg);

        // Speichere das Bild
        cv::imwrite(outputPath, visImg);

        safePrint("Match quality visualization saved to: " + outputPath, true);
    }
    catch (const std::exception& e) {
        safePrint("Error creating match visualization: " + std::string(e.what()), true);
    }
}

// Visualisiert die Blending-Regionen
void visualizeBlendingRegions(const cv::Mat& img1, const cv::Mat& img2,
                             int overlapWidth,
                             const std::string& outputPath,
                             const StitchConfig& config) {
    if (!config.verbose) return; // Nur ausführen, wenn verbose-Modus aktiv ist

    try {
        // Erstelle ein kombiniertes Bild
        cv::Mat visImg;

        // Beschränke die Bilddimensionen
        cv::Mat resizedImg1, resizedImg2;
        int maxHeight = 600;
        double scaleFactor = 1.0;

        if (img1.rows > maxHeight) {
            scaleFactor = static_cast<double>(maxHeight) / img1.rows;
            cv::resize(img1, resizedImg1, cv::Size(), scaleFactor, scaleFactor, cv::INTER_AREA);
            cv::resize(img2, resizedImg2, cv::Size(), scaleFactor, scaleFactor, cv::INTER_AREA);
        } else {
            resizedImg1 = img1.clone();
            resizedImg2 = img2.clone();
        }

        // Skaliere Überlappungsbreite
        int scaledOverlap = static_cast<int>(overlapWidth * scaleFactor);

        // Erstelle simuliertes Panorama
        int outputWidth = resizedImg1.cols + resizedImg2.cols - scaledOverlap;
        cv::Mat simulated(resizedImg1.rows, outputWidth, resizedImg1.type());

        // Kopiere Bild 1
        resizedImg1.copyTo(simulated(cv::Rect(0, 0, resizedImg1.cols, resizedImg1.rows)));

        // Erstelle Kopie für Überlagerung
        cv::Mat overlay = simulated.clone();

        // Markiere Überlappungsbereich im ersten Bild
        cv::rectangle(overlay,
                     cv::Rect(resizedImg1.cols - scaledOverlap, 0, scaledOverlap, resizedImg1.rows),
                     cv::Scalar(0, 0, 255), 2);

        // Markiere zweites Bild
        cv::rectangle(overlay,
                     cv::Rect(resizedImg1.cols, 0, resizedImg2.cols - scaledOverlap, resizedImg2.rows),
                     cv::Scalar(0, 255, 0), 2);

        // Markiere "Naht" wo die Bilder zusammentreffen
        cv::line(overlay,
                cv::Point(resizedImg1.cols - scaledOverlap/2, 0),
                cv::Point(resizedImg1.cols - scaledOverlap/2, resizedImg1.rows),
                cv::Scalar(255, 255, 0), 2);

        // Erstelle Label für die Bereiche
        cv::putText(overlay, "Image 1",
                   cv::Point(resizedImg1.cols/2 - 50, 30),
                   cv::FONT_HERSHEY_SIMPLEX, 0.8, cv::Scalar(255, 255, 255), 2);

        cv::putText(overlay, "Blend Region",
                   cv::Point(resizedImg1.cols - scaledOverlap/2 - 70, 30),
                   cv::FONT_HERSHEY_SIMPLEX, 0.8, cv::Scalar(255, 255, 255), 2);

        cv::putText(overlay, "Image 2",
                   cv::Point(resizedImg1.cols + (resizedImg2.cols - scaledOverlap)/2 - 50, 30),
                   cv::FONT_HERSHEY_SIMPLEX, 0.8, cv::Scalar(255, 255, 255), 2);

        // Blend overlay mit simuliertem Panorama
        double alpha = 0.7; // Transparenz
        cv::addWeighted(overlay, alpha, simulated, 1.0 - alpha, 0, visImg);

        // Informationstext
        std::stringstream ss;
        ss << "Overlap width: " << overlapWidth << "px";
        cv::putText(visImg, ss.str(),
                   cv::Point(20, visImg.rows - 20),
                   cv::FONT_HERSHEY_SIMPLEX, 0.7, cv::Scalar(255, 255, 255), 2);

        // Speichere das Bild
        cv::imwrite(outputPath, visImg);

        safePrint("Blending regions visualization saved to: " + outputPath, true);
    }
    catch (const std::exception& e) {
        safePrint("Error creating blending visualization: " + std::string(e.what()), true);
    }
}

// Visualisiere den Fortschritt des Panorama-Stitching
void visualizePanoramaProgress(const cv::Mat& panorama, int frameIdx,
                              const std::string& outputPath,
                              const StitchConfig& config) {
    if (!config.verbose) return;

    try {
        // Erstelle eine Kopie des Panoramas für die Visualisierung
        cv::Mat visImg;

        // Beschränke Höhe für Übersichtlichkeit
        int maxHeight = 600;
        double scaleFactor = 1.0;

        if (panorama.rows > maxHeight) {
            scaleFactor = static_cast<double>(maxHeight) / panorama.rows;
            cv::resize(panorama, visImg, cv::Size(), scaleFactor, scaleFactor);
        } else {
            visImg = panorama.clone();
        }

        // Füge Informationen hinzu
        std::stringstream ss;
        ss << "Frame: " << frameIdx << ", Size: " << panorama.cols << "x" << panorama.rows;
        cv::putText(visImg, ss.str(),
                   cv::Point(20, visImg.rows - 20),
                   cv::FONT_HERSHEY_SIMPLEX, 0.7, cv::Scalar(255, 255, 255), 2);

        // Speichere das Bild
        cv::imwrite(outputPath, visImg);

        safePrint("Panorama progress visualization saved to: " + outputPath, true);
    }
    catch (const std::exception& e) {
        safePrint("Error creating panorama progress visualization: " + std::string(e.what()), true);
    }
}