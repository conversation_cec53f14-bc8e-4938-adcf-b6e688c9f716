#include <opencv2/opencv.hpp>
#include <iostream>
#include <string>
#include <vector>
#include <algorithm>
#include <fstream>
#include <chrono>
#include <omp.h>
#include <direct.h>  // For _mkdir on Windows
#include <sys/stat.h> // For stat
#include "stitching/fs_util.h"

// Define DEBUG_PATH_LOGGING if not already defined by build system
// #define DEBUG_PATH_LOGGING

#include "stitching/config.h"
#include "stitching/image_loader.h"
#include "stitching/strip_extraction.h"

// Global mutex for thread-safe output
std::mutex coutMutex;

// Function declarations (implementation will be in separate files)
cv::Mat stitchTireSurfaceWithOpticalFlow(const std::string& inputFolder,
                                        const std::string& outputFolder,
                                        const std::string& filePattern,
                                        bool useBlending, int startIdx, int maxFrames,
                                        const StitchConfig& config,
                                        const std::string& serialNumber = "",
                                        const std::string& subprojectType = "");

cv::Mat enhancePanorama(const cv::Mat& panorama, const std::string& outputPath,
                       const StitchConfig& config);

int main(int argc, char** argv) {
    // Default parameters
    std::string inputFolder = "../../input/input_img";
    std::string outputFolder = "../../output";
    std::string filePattern = "IMG_*.JPG";
    bool useBlending = true;
    int startIdx = 0;
    int maxFrames = -1;
    std::string serialNumber = "";
    std::string subprojectType = "";

    // Configuration
    StitchConfig config;
    config.resizeScale = 1;      // No size change by default
    config.jpegQuality = 90;     // High JPEG quality
    config.verbose = true;       // Enable verbose for debugging 4K issues
    // config.templateMatchPrecision is initialized in StitchConfig constructor (currently 2)
    // config.stripWidth is initialized in StitchConfig constructor (currently 0)

    // Process command line arguments
    for (int i = 1; i < argc; i++) {
        std::string arg = argv[i];

        if (arg == "--input" && i + 1 < argc) {
            inputFolder = argv[++i];
#ifdef DEBUG_PATH_LOGGING
            std::cout << "DEBUG_PATH_LOGGING: Input folder set to: " << inputFolder << std::endl;
#endif
        }
        else if (arg == "--output" && i + 1 < argc) {
            outputFolder = argv[++i];
#ifdef DEBUG_PATH_LOGGING
            std::cout << "DEBUG_PATH_LOGGING: Output folder set to: " << outputFolder << std::endl;
#endif
        }
        else if (arg == "--file-pattern" && i + 1 < argc) {
            filePattern = argv[++i];
#ifdef DEBUG_PATH_LOGGING
            std::cout << "DEBUG_PATH_LOGGING: File pattern set to: " << filePattern << std::endl;
#endif
        }
        else if (arg == "--no-blending") {
            useBlending = false;
        }
        else if (arg == "--start-idx" && i + 1 < argc) {
            startIdx = std::stoi(argv[++i]);
        }
        else if (arg == "--max-frames" && i + 1 < argc) {
            maxFrames = std::stoi(argv[++i]);
        }
        else if (arg == "--resize-scale" && i + 1 < argc) {
            config.resizeScale = std::stod(argv[++i]);
        }
        else if (arg == "--jpeg-quality" && i + 1 < argc) {
            config.jpegQuality = std::stoi(argv[++i]);
        }
        else if (arg == "--verbose") {
            config.verbose = true;
        }
        else if (arg == "--serial-number" && i + 1 < argc) {
            serialNumber = argv[++i];
        }
        else if (arg == "--subproject-type" && i + 1 < argc) {
            subprojectType = argv[++i];
        }
        else if (arg == "--resolution-mode" && i + 1 < argc) { // Added for 4K/8K differentiation
            config.resolutionMode = argv[++i];
            safePrint("Command line: resolution-mode set to '" + config.resolutionMode + "'", true, config);
        }
        else if (arg == "--strip-width" && i + 1 < argc) {
            config.stripWidth = std::stoi(argv[++i]);
        }
        else if (arg == "--template-match-precision" && i + 1 < argc) {
            config.templateMatchPrecision = std::stoi(argv[++i]);
        }
        else if (arg == "--help") {
            std::cout << "Usage: " << argv[0] << " [options]" << std::endl;
            std::cout << "Options:" << std::endl;
            std::cout << "  --input <folder>       Input folder with images (default: input)" << std::endl;
            std::cout << "  --output <folder>      Output folder for panorama (default: output)" << std::endl;
            std::cout << "  --file-pattern <pat>   Pattern for image files (default: IMG_*.JPG)" << std::endl;
            std::cout << "  --no-blending          Disable image blending" << std::endl;
            std::cout << "  --start-idx <num>      Start processing from this image index (default: 0)" << std::endl;
            std::cout << "  --max-frames <num>     Maximum number of frames to process (default: all)" << std::endl;
            std::cout << "  --resize-scale <val>   Scale factor for image resizing (default: 1.0)" << std::endl;
            std::cout << "  --jpeg-quality <num>   JPEG quality for saved images (default: 90)" << std::endl;
            std::cout << "  --verbose              Enable verbose output" << std::endl;
            std::cout << "  --serial-number <str>   Serial number for unique filenames" << std::endl;
            std::cout << "  --subproject-type <str> Subproject type (frontal, left, right) for unique filenames" << std::endl;
            std::cout << "  --resolution-mode <str> Resolution mode (4K or 8K) for stitching parameters" << std::endl; // Added for 4K/8K differentiation
            std::cout << "  --strip-width <num>    Strip width for stitching (default: 0, auto-calculated or 4K specific)" << std::endl;
            std::cout << "  --template-match-precision <num> Template matching precision (default: 2, or 4K specific)" << std::endl;
            std::cout << "  --help                 Display this help message" << std::endl;
            return 0;
        }
    }

    // Debug: Show resolution mode that was received
    safePrint("Resolution mode received: '" + config.resolutionMode + "'", true, config);

    // Post-argument processing for 4K mode defaults
    if (config.resolutionMode == "4K") {
        safePrint("*** 4K MODE DETECTED - APPLYING 4K OPTIMIZATIONS ***", true, config);
        if (config.stripWidth == 0) { // Not set by CLI
            config.stripWidth = 40; // Increased default strip width for 4K (was 20, now 40)
            safePrint("4K mode: stripWidth not set by CLI, using optimized default: " + std::to_string(config.stripWidth), true, config);
        }
        // Assuming templateMatchPrecision has a default in its constructor (e.g. 2)
        // If it's still that default, and we want a different one for 4K.
        // Let's say the original default was 2.
        if (config.templateMatchPrecision == 2) { // Check if it's the original default
            config.templateMatchPrecision = 2; // Keep high precision for 4K (was 1, now 2)
            safePrint("4K mode: templateMatchPrecision is default, using 4K optimized: " + std::to_string(config.templateMatchPrecision), true, config);
        }
        safePrint("*** 4K MODE CONFIGURATION COMPLETE ***", true, config);
    } else {
        safePrint("*** 8K/DEFAULT MODE DETECTED ***", true, config);
    }

    try {
        // Initialize OpenMP
        int maxThreads = omp_get_max_threads();
        std::cout << "OpenMP using up to " << maxThreads << " threads" << std::endl;

        std::cout << "\nStarting tire surface stitching with optical flow integration..." << std::endl;

        // Run the stitching with optical flow
        cv::Mat result = stitchTireSurfaceWithOpticalFlow(
            inputFolder,
            outputFolder,
            filePattern,
            useBlending,
            startIdx,
            maxFrames,
            config,
            serialNumber,
            subprojectType
        );

        std::cout << "Processing completed! Final image size: " << result.size() << std::endl;

        // Create an enhanced version (optional)
        std::string filePrefix = "tire_unwrapped";

        // If serial number and subproject type are provided, use them for the filename
        if (!serialNumber.empty() && !subprojectType.empty()) {
            // Create a safe filename from the serial number
            std::string safeSerial = serialNumber;
            for (char& c : safeSerial) {
                if (!std::isalnum(c)) {
                    c = '_';
                }
            }

            // Create a timestamp
            auto now = std::chrono::system_clock::now();
            auto now_time_t = std::chrono::system_clock::to_time_t(now);
            std::tm now_tm = *std::localtime(&now_time_t);
            char timestamp[20];
            std::strftime(timestamp, sizeof(timestamp), "%Y%m%d_%H%M%S", &now_tm);

            filePrefix = "tire_" + safeSerial + "_" + subprojectType + "_" + timestamp;
        }

        std::string enhancedOutputPath = outputFolder + "/" + filePrefix + "_enhanced.JPG";
#ifdef DEBUG_PATH_LOGGING
        std::cout << "DEBUG_PATH_LOGGING: Enhanced output path: " << enhancedOutputPath << std::endl;
#endif
        try {
            cv::Mat enhanced = enhancePanorama(result, enhancedOutputPath, config);
        }
        catch (const std::exception& e) {
            std::cout << "Warning: Failed to create enhanced version: " << e.what() << std::endl;
        }

        std::cout << "\nAll processing completed successfully!" << std::endl;
    }
    catch (const std::exception& e) {
        std::cout << "Error: " << e.what() << std::endl;
        return 1;
    }

    return 0;
}