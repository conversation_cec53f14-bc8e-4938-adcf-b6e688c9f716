"""
Subproject dialogs for the Tire Panorama Tool.

Contains dialogs for managing subproject settings.
"""
import os
import tkinter as tk
from tkinter import ttk, messagebox

def show_subproject_settings_dialog(app, subproject_type):
    """
    Show dialog to configure subproject settings.

    Args:
        app: Main application instance
        subproject_type: Type of subproject ("frontal", "left", or "right")

    Returns:
        Dictionary of settings if confirmed, None if cancelled
    """
    # Get current subproject
    if not app.current_project:
        return None

    subproject = app.current_project.subprojects.get(subproject_type, {})

    # Create dialog window
    dialog = tk.Toplevel(app.root)
    dialog.title(f"{subproject_type.title()} Subproject Settings")
    dialog.geometry("500x400")
    dialog.transient(app.root)
    dialog.grab_set()

    # Make dialog modal
    dialog.focus_set()

    # Center dialog
    x = app.root.winfo_x() + (app.root.winfo_width() // 2) - (500 // 2)
    y = app.root.winfo_y() + (app.root.winfo_height() // 2) - (400 // 2)
    dialog.geometry(f"+{x}+{y}")

    # Store result
    result = [None]

    # Create content
    content_frame = ttk.Frame(dialog, padding=10)
    content_frame.pack(fill=tk.BOTH, expand=True)

    # Title
    ttk.Label(
        content_frame,
        text=f"{subproject_type.title()} Subproject Settings",
        font=("Segoe UI", 12, "bold")
    ).pack(pady=(0, 10))

    # Input info frame
    input_frame = ttk.LabelFrame(content_frame, text="Input Information")
    input_frame.pack(fill=tk.X, pady=10)

    input_path = subproject.get("input_path", "None")
    input_type = subproject.get("input_type", "None")

    # Display input info
    input_info_frame = ttk.Frame(input_frame)
    input_info_frame.pack(fill=tk.X, padx=10, pady=5)

    ttk.Label(input_info_frame, text="Input Type:", width=15, anchor=tk.W).grid(row=0, column=0, sticky=tk.W, pady=2)
    ttk.Label(input_info_frame, text=input_type.title() if input_type else "None").grid(row=0, column=1, sticky=tk.W, pady=2)

    ttk.Label(input_info_frame, text="Input Path:", width=15, anchor=tk.W).grid(row=1, column=0, sticky=tk.W, pady=2)
    ttk.Label(
        input_info_frame,
        text=input_path if input_path else "None",
        wraplength=350
    ).grid(row=1, column=1, sticky=tk.W, pady=2)

    # Processing parameters frame
    params_frame = ttk.LabelFrame(content_frame, text="Processing Parameters")
    params_frame.pack(fill=tk.BOTH, expand=True, pady=10)

    # Get current parameters
    parameters = subproject.get("parameters", {})

    # --- BEGIN ADDED CODE FOR INPUT RESOLUTION ---
    # Determine initial resolution for the radio button
    saved_resolution = parameters.get("input_resolution")
    initial_resolution_for_var = "8k" # Default

    if saved_resolution:
        initial_resolution_for_var = saved_resolution
    else:
        # Default to AUTO mode for automatic detection
        initial_resolution_for_var = "auto"

    input_resolution_var = tk.StringVar(value=initial_resolution_for_var)

    # Frame for input resolution radio buttons
    resolution_frame = ttk.Frame(params_frame)
    resolution_frame.pack(fill=tk.X, padx=10, pady=(10, 5)) # Add some top padding

    ttk.Label(resolution_frame, text="Input Resolution:", width=15, anchor=tk.W).pack(side=tk.LEFT)

    radio_auto = ttk.Radiobutton(resolution_frame, text="AUTO", variable=input_resolution_var, value="auto")
    radio_auto.pack(side=tk.LEFT, padx=(0, 5))

    radio_4k = ttk.Radiobutton(resolution_frame, text="4K", variable=input_resolution_var, value="4k")
    radio_4k.pack(side=tk.LEFT, padx=(0, 5))

    radio_8k = ttk.Radiobutton(resolution_frame, text="8K", variable=input_resolution_var, value="8k")
    radio_8k.pack(side=tk.LEFT)
    # --- END ADDED CODE FOR INPUT RESOLUTION ---

    # Strip width
    # strip_width_frame = ttk.Frame(params_frame)
    # strip_width_frame.pack(fill=tk.X, padx=10, pady=5)

    # ttk.Label(strip_width_frame, text="Strip Width:", width=15, anchor=tk.W).pack(side=tk.LEFT)

    strip_width_var = tk.StringVar(value=str(parameters.get("strip_width", "")))
    # strip_width_entry = ttk.Entry(strip_width_frame, textvariable=strip_width_var, width=10)
    # strip_width_entry.pack(side=tk.LEFT)

    # ttk.Label(strip_width_frame, text="pixels").pack(side=tk.LEFT, padx=5)
    # ttk.Label(
    #     strip_width_frame,
    #     text="(Width of strip from each frame. Leave empty for automatic detection.)",
    #     foreground="#888888"
    # ).pack(side=tk.LEFT, padx=10)

    # Number of samples
    # samples_frame = ttk.Frame(params_frame)
    # samples_frame.pack(fill=tk.X, padx=10, pady=5)

    # ttk.Label(samples_frame, text="Number of Samples:", width=15, anchor=tk.W).pack(side=tk.LEFT)

    samples_var = tk.StringVar(value=str(parameters.get("num_samples", "")))
    # samples_entry = ttk.Entry(samples_frame, textvariable=samples_var, width=10)
    # samples_entry.pack(side=tk.LEFT)

    # ttk.Label(
    #     samples_frame,
    #     text="(Frames to extract. Leave empty for all frames.)",
    #     foreground="#888888"
    # ).pack(side=tk.LEFT, padx=10)

    # Max frames
    max_frames_frame = ttk.Frame(params_frame)
    max_frames_frame.pack(fill=tk.X, padx=10, pady=5)

    ttk.Label(max_frames_frame, text="Max Frames:", width=15, anchor=tk.W).pack(side=tk.LEFT)

    max_frames_var = tk.StringVar(value=str(parameters.get("max_frames", "")))
    max_frames_entry = ttk.Entry(max_frames_frame, textvariable=max_frames_var, width=10)
    max_frames_entry.pack(side=tk.LEFT)

    ttk.Label(
        max_frames_frame,
        text="(Maximum frames to process. Leave empty for all frames.)",
        foreground="#888888"
    ).pack(side=tk.LEFT, padx=10)

    # Blending option
    # blending_frame = ttk.Frame(params_frame)
    # blending_frame.pack(fill=tk.X, padx=10, pady=5)

    blending_var = tk.BooleanVar(value=parameters.get("enable_blending", True))
    # blending_cb = ttk.Checkbutton(
    #     blending_frame,
    #     text="Enable Blending",
    #     variable=blending_var
    # )
    # blending_cb.pack(anchor=tk.W)

    # ttk.Label(
    #     blending_frame,
    #     text="(Enable blending between frames for smoother transitions.)",
    #     foreground="#888888"
    # ).pack(anchor=tk.W, padx=(25, 0))

    # Rotate frames option
    rotate_frame = ttk.Frame(params_frame)
    rotate_frame.pack(fill=tk.X, padx=10, pady=5)

    rotate_var = tk.BooleanVar(value=parameters.get("rotate_frames", True))
    rotate_cb = ttk.Checkbutton(
        rotate_frame,
        text="Rotate Frames",
        variable=rotate_var
    )
    rotate_cb.pack(anchor=tk.W)

    ttk.Label(
        rotate_frame,
        text="(Rotate frames 90 degrees clockwise during extraction.)",
        foreground="#888888"
    ).pack(anchor=tk.W, padx=(25, 0))

    # Buttons
    buttons_frame = ttk.Frame(content_frame)
    buttons_frame.pack(fill=tk.X, pady=10)

    def on_cancel():
        dialog.destroy()

    def on_save():
        # Validate strip width
        strip_width = None
        if strip_width_var.get().strip():
            try:
                strip_width = int(strip_width_var.get())
                if strip_width <= 0:
                    messagebox.showerror("Error", "Strip width must be positive", parent=dialog)
                    return
            except ValueError:
                messagebox.showerror("Error", "Strip width must be a number", parent=dialog)
                return

        # Validate number of samples
        num_samples = None
        if samples_var.get().strip():
            num_samples = samples_var.get().strip()

        # Validate max frames
        max_frames = None
        if max_frames_var.get().strip():
            try:
                max_frames = int(max_frames_var.get())
                if max_frames <= 0:
                    messagebox.showerror("Error", "Max frames must be positive", parent=dialog)
                    return
            except ValueError:
                messagebox.showerror("Error", "Max frames must be a number", parent=dialog)
                return

        # Create result
        settings = {
            "strip_width": strip_width,
            "num_samples": num_samples,
            "max_frames": max_frames,
            "enable_blending": blending_var.get(),
            "rotate_frames": rotate_var.get(),
            "input_resolution": input_resolution_var.get() # Add selected resolution
        }

        # Update the subproject directly so changes are stored immediately
        if app.current_project and app.current_project.subprojects.get(subproject_type):
            app.current_project.subprojects[subproject_type]["parameters"] = settings
            app.project_manager.save_project(app.current_project)
            app.status_var.set(f"Settings for {subproject_type} saved successfully")

        result[0] = settings
        dialog.destroy()

    ttk.Button(buttons_frame, text="Cancel", command=on_cancel).pack(side=tk.RIGHT, padx=5)
    save_btn = ttk.Button(buttons_frame, text="Save Settings", command=on_save, style="Accent.TButton")
    save_btn.pack(side=tk.RIGHT, padx=5)

    # Give the save button initial focus to make it more prominent
    save_btn.focus_set()

    # Bind Enter key
    dialog.bind("<Return>", lambda event: on_save())
    dialog.bind("<Escape>", lambda event: on_cancel())

    # Wait for dialog to close
    app.root.wait_window(dialog)

    return result[0]

def show_subproject_cleanup_confirm_dialog(app, subproject_type):
    """
    Show confirmation dialog for frame cleanup.

    Args:
        app: Main application instance
        subproject_type: Type of subproject ("frontal", "left", or "right")

    Returns:
        True if confirmed, False if cancelled
    """
    # Get current subproject
    if not app.current_project:
        return False

    subproject = app.current_project.subprojects.get(subproject_type, {})

    # Check if cleanup is possible
    if subproject.get("status") != "completed":
        messagebox.showinfo("Info", "Cleanup is only possible for completed panoramas.")
        return False

    # Get frames directory
    frames_dir = os.path.join(app.current_project.project_dir, subproject_type, "frames")

    # Count frames
    frame_count = 0
    if os.path.exists(frames_dir):
        frame_count = len([f for f in os.listdir(frames_dir) if os.path.isfile(os.path.join(frames_dir, f))])

    if frame_count == 0:
        messagebox.showinfo("Info", "No frames to clean up.")
        return False

    # Calculate space in MB
    space_mb = 0
    for root, _, files in os.walk(frames_dir):
        for file in files:
            file_path = os.path.join(root, file)
            if os.path.isfile(file_path):
                space_mb += os.path.getsize(file_path) / (1024 * 1024)

    # Create dialog window
    dialog = tk.Toplevel(app.root)
    dialog.title("Confirm Frame Cleanup")
    dialog.geometry("400x250")
    dialog.transient(app.root)
    dialog.grab_set()

    # Make dialog modal
    dialog.focus_set()

    # Center dialog
    x = app.root.winfo_x() + (app.root.winfo_width() // 2) - (400 // 2)
    y = app.root.winfo_y() + (app.root.winfo_height() // 2) - (250 // 2)
    dialog.geometry(f"+{x}+{y}")

    # Store result
    result = [False]

    # Create content
    content_frame = ttk.Frame(dialog, padding=10)
    content_frame.pack(fill=tk.BOTH, expand=True)

    # Warning icon
    warning_frame = ttk.Frame(content_frame)
    warning_frame.pack(fill=tk.X, pady=10)

    # Use a text-based warning if we don't have an icon
    warning_label = ttk.Label(
        warning_frame,
        text="⚠️",
        font=("Segoe UI", 24)
    )
    warning_label.pack()

    # Title
    ttk.Label(
        content_frame,
        text="Confirm Frame Cleanup",
        font=("Segoe UI", 12, "bold")
    ).pack(pady=(0, 10))

    # Info text
    info_text = (
        f"This will permanently delete {frame_count} frames\n"
        f"for the {subproject_type} panorama, freeing approximately\n"
        f"{space_mb:.1f} MB of disk space.\n\n"
        "The final panorama will be preserved."
    )

    ttk.Label(
        content_frame,
        text=info_text,
        justify=tk.CENTER
    ).pack(pady=10)

    # Buttons
    buttons_frame = ttk.Frame(content_frame)
    buttons_frame.pack(fill=tk.X, pady=10)

    def on_cancel():
        dialog.destroy()

    def on_confirm():
        result[0] = True
        dialog.destroy()

    ttk.Button(buttons_frame, text="Cancel", command=on_cancel).pack(side=tk.RIGHT, padx=5)
    ttk.Button(buttons_frame, text="Delete Frames", command=on_confirm, style="Accent.TButton").pack(side=tk.RIGHT, padx=5)

    # Bind Escape key
    dialog.bind("<Escape>", lambda event: on_cancel())

    # Wait for dialog to close
    app.root.wait_window(dialog)

    return result[0]